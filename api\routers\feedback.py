"""
This module handles interview answer feedback generation and management.
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import List
from dotenv import load_dotenv
from api.utils.qa_model import generate_feedback_response
from api.database.prompt_db import fetch_latest_prompt
from api.database.feedback_db import (
    load_feedback_examples,
    add_feedback_example as db_add_feedback_example,
    delete_feedback_example as db_delete_feedback_example,
    export_feedback_examples_to_csv,
    import_feedback_examples_from_csv
)
from api.common.api_logger import api_logger as logger
from api.common.dependencies import get_api_key
from fastapi import Depends
from api.common.pydantic_models import (
    Feedback,
    FeedbackRequest,
    FeedbackExample,
    FeedbackExampleCreate,
    ApiResponse,
    CsvUploadResponse
)
import io
import csv

load_dotenv(override=True)
router = APIRouter(
    prefix="/feedback",
    tags=["Feedback"]
)

# No need to check if the table exists - it's already created in MySQL

@router.post("/generate-feedback", response_model=Feedback)
def generate_feedback(request: FeedbackRequest, token: bool = Depends(get_api_key)) -> Feedback:
    """Generate feedback for an interview answer"""
    try:
        logger.info(f"Generating feedback for question: {request.question}")
        prompt = fetch_latest_prompt("feedback")  # Get prompt from prompt management
        messages = [
            {"role": "system", "content": prompt},
            {
                "role": "user",
                "content": f"Question: {request.question}\nAnswer: {request.answer}"
            }
        ]

        # generate_response returns a tuple (response_dict, token_usage)
        response_tuple = generate_feedback_response(messages, request.model)
        if not response_tuple or not isinstance(response_tuple, tuple):
            raise ValueError("Invalid response format from model")

        response_dict = response_tuple[0]
        if not response_dict or "suggestions" not in response_dict or "proposed_answer" not in response_dict:
            raise ValueError("Response missing required fields")

        # Convert the response format to Feedback model
        feedback = Feedback(
            suggestions=response_dict["suggestions"],
            proposed_answer=response_dict["proposed_answer"]
        )

        return feedback

    except Exception as e:
        logger.error(f"Error generating feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list-feedback-examples", response_model=List[FeedbackExample])
def get_feedback_examples() -> List[FeedbackExample]:
    """Get all feedback examples"""
    return load_feedback_examples()

@router.post("/add-feedback-examples", response_model=ApiResponse)
def add_feedback_example(example: FeedbackExampleCreate) -> ApiResponse:
    """Add a new feedback example"""
    # Convert FeedbackExampleCreate to FeedbackExample with a temporary empty ID
    feedback_example = FeedbackExample(
        id="",  # This will be replaced in db_add_feedback_example
        question=example.question,
        answer=example.answer,
        suggestions=example.suggestions,
        proposed_answer=example.proposed_answer
    )

    success, added_example = db_add_feedback_example(feedback_example)
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to add feedback example"
        )
    return ApiResponse(message="Feedback example added successfully", id=added_example.id)

@router.delete("/delete-feedback-examples", response_model=ApiResponse)
def delete_feedback_example(id: str) -> ApiResponse:
    """Delete a feedback example by ID"""
    success = db_delete_feedback_example(id)
    if not success:
        raise HTTPException(
            status_code=404,
            detail="Feedback example not found"
        )

    return ApiResponse(message="Feedback example deleted successfully", id=id)

@router.get("/list-feedback-examples-csv")
def get_feedback_examples_csv():
    """Download all feedback examples as a CSV file"""
    try:
        success, csv_data = export_feedback_examples_to_csv()
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to export feedback examples to CSV"
            )

        # Create a response with the CSV file
        return StreamingResponse(
            iter([csv_data.getvalue()]),
            media_type="text/csv",
            headers={
                "Content-Disposition": "attachment; filename=feedback_examples.csv"
            }
        )
    except Exception as e:
        logger.error(f"Error exporting feedback examples to CSV: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/add-feedback-examples-csv", response_model=CsvUploadResponse)
async def add_feedback_examples_csv(
    file: UploadFile = File(...)
) -> CsvUploadResponse:
    """
    Upload feedback examples from a CSV file,

    Required CSV columns:
    - question: The interview question
    - answer: The original answer provided by the candidate
    - suggestions: Improvement suggestions for the answer
    - proposed_answer: An improved version of the original answer

    Note: All existing feedback examples will be replaced with the ones from the uploaded CSV.
    """
    try:
        # Check file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=400,
                detail="Only CSV files are accepted"
            )

        # Read file content
        contents = await file.read()

        # Step 1: Validate CSV file can be read and parsed
        try:
            content_str = contents.decode('utf-8')

            # Check for extra text before CSV data
            lines = content_str.strip().split('\n')
            if lines:
                first_line = lines[0].strip()
                # Check if first line looks like it could be CSV headers
                if first_line and not any(col in first_line.lower() for col in ['question', 'answer', 'suggestions', 'proposed']):
                    # Check if it looks like extra text instead of CSV
                    if len(first_line.split(',')) < 3 or any(phrase in first_line.lower() for phrase in [
                        'this is', 'note:', 'instructions:', 'please', 'make sure', 'important:', 'readme'
                    ]):
                        raise HTTPException(
                            status_code=400,
                            detail="Failed to parse file. The file appears to have extra text before CSV data. Make sure it's a proper CSV file starting with headers: question, answer, suggestions, proposed_answer"
                        )

            csv_file_for_validation = io.StringIO(content_str)
        except UnicodeDecodeError:
            raise HTTPException(
                status_code=400,
                detail="Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
            )
        except HTTPException:
            raise
        except Exception:
            raise HTTPException(
                status_code=400,
                detail="Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
            )

        # Step 2: Validate CSV structure and required columns
        try:
            reader = csv.DictReader(csv_file_for_validation)
            fieldnames = reader.fieldnames

            if not fieldnames:
                raise HTTPException(
                    status_code=400,
                    detail="Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
                )

            # Check if all required columns exist
            required_columns = ['question', 'answer', 'suggestions', 'proposed_answer']
            missing_columns = [col for col in required_columns if col not in fieldnames]

            if missing_columns:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer. Missing columns: {', '.join(missing_columns)}"
                )

            # Check for extra columns that might indicate invalid CSV format
            extra_columns = [col for col in fieldnames if col not in required_columns and col is not None and col.strip()]
            if extra_columns:
                # Check if extra columns look like they might be data instead of headers
                suspicious_extras = []
                for col in extra_columns:
                    # If column name is very long or contains typical data patterns, it might be misplaced data
                    if len(col) > 50 or any(phrase in col.lower() for phrase in ['i am', 'my experience', 'years', 'skills', 'worked']):
                        suspicious_extras.append(col[:50] + "..." if len(col) > 50 else col)

                if suspicious_extras:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Failed to parse file. The file appears to have extra text or invalid CSV format. Make sure it's a proper CSV file with only the required columns: question, answer, suggestions, proposed_answer. Found suspicious content: {', '.join(suspicious_extras)}"
                    )

        except HTTPException:
            raise
        except Exception as e:
            # Check if the error suggests invalid CSV format
            error_str = str(e).lower()
            if any(phrase in error_str for phrase in ['delimiter', 'quote', 'escape', 'line']):
                raise HTTPException(
                    status_code=400,
                    detail="Failed to parse file. The file appears to have invalid CSV format or extra text. Make sure it's a proper CSV file with columns: question, answer, suggestions, proposed_answer"
                )
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
                )

        # Step 3: Validate rows and count valid/invalid ones
        csv_file_for_validation.seek(0)  # Reset to beginning
        reader = csv.DictReader(csv_file_for_validation)
        valid_rows = 0
        skipped_rows = 0
        skipped_details = []
        row_number = 1  # Start from 1 (header is row 0)

        for row in reader:
            row_number += 1

            # Check if all required fields have values (not empty or just whitespace)
            missing_values = []
            for col in required_columns:
                value = row.get(col, '').strip()
                if not value:
                    missing_values.append(col)

            if missing_values:
                skipped_rows += 1
                if len(skipped_details) < 3:  # Only store first 3 for display
                    skipped_details.append(f"Row {row_number}: missing {', '.join(missing_values)}")
            else:
                valid_rows += 1

        # If no valid rows, return error
        if valid_rows == 0:
            if skipped_rows > 0:
                detail_msg = f"No valid rows found. All {skipped_rows} rows have missing required values. "
                if skipped_details:
                    detail_msg += f"Examples: {'; '.join(skipped_details)}"
                    if skipped_rows > 3:
                        detail_msg += f" and {skipped_rows - 3} more..."
                raise HTTPException(status_code=400, detail=detail_msg)
            else:
                raise HTTPException(status_code=400, detail="No data rows found in CSV file")

        # Step 4: If validation passed, proceed with import
        csv_file = io.BytesIO(contents)
        success, count = import_feedback_examples_from_csv(csv_file)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to import feedback examples from CSV"
            )

        # Step 5: Return success message with details
        success_msg = f"Successfully imported {count} feedback examples from CSV. All previous examples have been replaced."
        if skipped_rows > 0:
            success_msg += f" Note: {skipped_rows} rows were skipped due to missing required values."

        return CsvUploadResponse(message=success_msg)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error importing feedback examples from CSV: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

