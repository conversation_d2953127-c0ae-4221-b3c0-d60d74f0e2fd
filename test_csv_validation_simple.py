"""
Simple test to validate CSV parsing logic without database connection.
This tests the validation logic we added to the router.
"""

import io
import csv


def test_csv_validation_logic():
    """Test the CSV validation logic similar to what's in the router."""
    
    def validate_csv_content(content_str):
        """Simulate the validation logic from the router."""
        try:
            csv_file_for_validation = io.StringIO(content_str)
            reader = csv.DictReader(csv_file_for_validation)
            fieldnames = reader.fieldnames
            
            if not fieldnames:
                return False, "Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
            
            # Check if all required columns exist
            required_columns = ['question', 'answer', 'suggestions', 'proposed_answer']
            missing_columns = [col for col in required_columns if col not in fieldnames]
            
            if missing_columns:
                return False, f"Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer. Missing columns: {', '.join(missing_columns)}"
            
            # Validate rows
            csv_file_for_validation.seek(0)
            reader = csv.DictReader(csv_file_for_validation)
            valid_rows = 0
            skipped_rows = 0
            skipped_details = []
            row_number = 1
            
            for row in reader:
                row_number += 1
                
                missing_values = []
                for col in required_columns:
                    value = row.get(col, '').strip()
                    if not value:
                        missing_values.append(col)
                
                if missing_values:
                    skipped_rows += 1
                    if len(skipped_details) < 3:
                        skipped_details.append(f"Row {row_number}: missing {', '.join(missing_values)}")
                else:
                    valid_rows += 1
            
            if valid_rows == 0:
                if skipped_rows > 0:
                    detail_msg = f"No valid rows found. All {skipped_rows} rows have missing required values. "
                    if skipped_details:
                        detail_msg += f"Examples: {'; '.join(skipped_details)}"
                        if skipped_rows > 3:
                            detail_msg += f" and {skipped_rows - 3} more..."
                    return False, detail_msg
                else:
                    return False, "No data rows found in CSV file"
            
            success_msg = f"Validation passed. Found {valid_rows} valid rows."
            if skipped_rows > 0:
                success_msg += f" {skipped_rows} rows would be skipped."
            
            return True, success_msg
            
        except Exception as e:
            return False, f"Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer. Error: {str(e)}"
    
    # Test cases
    test_cases = [
        {
            "name": "Valid CSV",
            "content": """question,answer,suggestions,proposed_answer
"What are your strengths?","I am good at programming","Be more specific","I excel at Python programming"
"Tell me about yourself","I am a developer","Add more details","I am a software developer with 3 years experience"
""",
            "expected_success": True
        },
        {
            "name": "Missing columns",
            "content": """question,answer,suggestions
"What are your strengths?","I am good at programming","Be more specific"
""",
            "expected_success": False
        },
        {
            "name": "Empty values",
            "content": """question,answer,suggestions,proposed_answer
"What are your strengths?","I am good at programming","Be more specific","I excel at Python programming"
"Tell me about yourself","","Add more details","I am a software developer"
"","I am a developer","",""
""",
            "expected_success": True  # Should pass but with skipped rows
        },
        {
            "name": "All empty rows",
            "content": """question,answer,suggestions,proposed_answer
"","","",""
"","","",""
""",
            "expected_success": False
        },
        {
            "name": "Only headers",
            "content": """question,answer,suggestions,proposed_answer
""",
            "expected_success": False
        },
        {
            "name": "Invalid CSV format",
            "content": """This is not a CSV
Just plain text
""",
            "expected_success": False
        }
    ]
    
    print("Testing CSV validation logic...")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print("-" * 30)
        
        success, message = validate_csv_content(test_case['content'])
        
        print(f"Expected success: {test_case['expected_success']}")
        print(f"Actual success: {success}")
        print(f"Message: {message}")
        
        if success == test_case['expected_success']:
            print("✅ PASS")
        else:
            print("❌ FAIL")
    
    print("\n" + "=" * 50)
    print("CSV validation tests completed!")


if __name__ == "__main__":
    test_csv_validation_logic()
